<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:orientation="vertical"
            android:padding="16dp">

            <EditText
                android:id="@+id/editText"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:clickable="false"
                android:cursorVisible="false"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:hint="@string/hint_input_text"
                android:inputType="none"
                tools:ignore="TouchTargetSizeCheck" />


            <Button
                android:id="@+id/btnQRCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="10dp"
                android:onClick="doQRCodeClick"
                android:text="@string/scan_qrcode"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnBarCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doBarCodeClick"
                android:text="@string/scan_barcode" />

            <Button
                android:id="@+id/btnTextOcr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doTextOcrClick"
                android:text="@string/ocr" />

            <Button
                android:id="@+id/btnFace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doFaceClick"
                android:text="@string/face_recognition" />

            <Button
                android:id="@+id/btnFingerprint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doFingerprintClick"
                android:text="@string/fingerprint_recognition" />

            <Button
                android:id="@+id/btnUsbSerialPort"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doUsbSerialPort"
                android:text="@string/usb_serial_port"
                android:visibility="gone" />

            <Button
                android:id="@+id/btnSerialPort"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doSerialPort"
                android:text="@string/standard_serial_port" />


            <Button
                android:id="@+id/btnLanguageSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doLanguageSettings"
                android:text="@string/language_settings"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/btnCheckUpdate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doCheckUpdate"
                android:text="@string/check_update"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/btnCommand"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:text="@string/text_command"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/btnVoiceInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:onClick="doVoiceInput"
                android:background="@drawable/button_primary"
                android:text="@string/voice_input"
                android:textColor="@color/white" />


            <Button
                android:id="@+id/btnChat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/button_primary"
                android:onClick="doChatClick"
                android:text="@string/text_chat"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/btnTestBatteryOptimization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:visibility="gone"
                android:background="@drawable/button_primary"
                android:onClick="doTestBatteryOptimization"
                android:text="@string/test_battery_optimization"
                android:textColor="@color/white" />
        </LinearLayout>

    </ScrollView>
</layout>