package com.supreme.smart.sewing.voice.example;

import android.annotation.SuppressLint;
import android.media.AudioTrack;
import android.media.MediaPlayer;
import android.media.SoundPool;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.R;
import com.supreme.smart.sewing.databinding.ChatBinding;
import com.supreme.smart.sewing.voice.adapter.ChatMessage;
import com.supreme.smart.sewing.voice.adapter.ChatMessageAdapter;
import com.supreme.smart.sewing.voice.component.VoiceUsageType;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionAction;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionCallback;
import com.supreme.smart.sewing.voice.voice.VoiceRecognitionExecutor;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 聊天界面示例 - 微信风格
 * 支持三种操作：直接松开转换文字后发送文字、左滑取消，上滑发送录音
 */
public class ChatActivity extends AppCompatActivity {
    private static final String TAG = "ChatActivity";


    // DataBinding
    private ChatBinding binding;
    private ChatViewModel viewModel;

    // 核心组件 - 使用RxJava版本的VoiceRecognitionExecutor
    private VoiceRecognitionExecutor voiceExecutor;
    private ChatMessageAdapter chatAdapter;
    private List<ChatMessage> chatMessages;

    // 播放相关
    private MediaPlayer currentMediaPlayer = null;
    private AudioTrack currentAudioTrack = null;
    private String currentPlayingMessageId = null;
    private Thread audioTrackThread = null;

    // 录音转文字相关
    private String convertingMessageId = null;  // 正在转换的消息ID
    // 声音提示相关
    private SoundPool soundPool;

    private final Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 启用边缘到边缘(EdgeToEdge)显示模式，让内容延伸到系统栏(状态栏和导航栏)下面
        EdgeToEdge.enable(this);

        // 初始化DataBinding
        binding = DataBindingUtil.setContentView(this, R.layout.chat);
        viewModel = new ChatViewModel();
        binding.setViewModel(viewModel);
        binding.setLifecycleOwner(this);

        initViews();
        initChatList();
        setupVoiceExecutor();
        setupClickListeners();
        setupTextWatcher();
    }

    private void initViews() {
        // 设置发送按钮初始状态为禁用
        binding.btnSend.setEnabled(false);
        binding.btnSend.setAlpha(0.5f);
        binding.btnSend.setScaleX(0.90f);
        binding.btnSend.setScaleY(0.90f);
    }

    private void initChatList() {
        chatMessages = new ArrayList<>();
        chatAdapter = new ChatMessageAdapter(this, chatMessages);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setStackFromEnd(true);
        binding.rvChatMessages.setLayoutManager(layoutManager);
        binding.rvChatMessages.setAdapter(chatAdapter);

        // 设置语音消息点击监听
        chatAdapter.setOnVoiceMessageClickListener(this::playVoiceMessage);

        // 设置语音消息长按监听
        chatAdapter.setOnVoiceMessageLongClickListener(this::convertVoiceToText);
    }

    /**
     * 设置VoiceRecognitionExecutor - 核心简化！
     */
    private void setupVoiceExecutor() {
        // 创建VoiceRecognitionExecutor实例
        voiceExecutor = new VoiceRecognitionExecutor();

        // 初始化执行器，指定为聊天场景
        voiceExecutor.initialize(this, new VoiceRecognitionCallback() {
            @Override
            public void onRealTimeTextUpdate(String text) {
                LogUtils.dTag(TAG, "实时文本更新: " + text);
                // 聊天场景下实时文本更新通过对话框显示，这里可以不处理
            }

            @Override
            public void onRecognitionComplete(String text, VoiceRecognitionAction action) {
                LogUtils.dTag(TAG, "识别完成 - 文本: " + text + ", 操作: " + action);

                switch (action) {
                    case SEND_TEXT:
                        // 直接松开 -> 发送文字消息
                        if (!TextUtils.isEmpty(text)) {
                            sendTextMessage(text);
                        } else {
                            Toast.makeText(ChatActivity.this, R.string.toast_no_recognition, Toast.LENGTH_SHORT).show();
                        }
                        break;

                    case SEND_VOICE:
                        // 上滑 -> 发送语音消息（不转文字）
                        LogUtils.dTag(TAG, "用户选择发送语音消息");
                        break;

                    case CANCEL:
                        // 左滑 -> 取消
                        LogUtils.dTag(TAG, "用户取消了录音");
                        break;
                }
            }

            @Override
            public void onAudioDataCollected(byte[] audioData) {
                LogUtils.dTag(TAG, "收集到音频数据，大小: " + (audioData != null ? audioData.length : 0)) ;
                if (audioData != null && audioData.length > 0) {
                    sendVoiceMessage(audioData);
                }
            }

            @Override
            public void onActionChanged(VoiceRecognitionAction newAction) {
                LogUtils.dTag(TAG, "操作变更: " + newAction);
                // 可以在这里添加UI反馈，比如按钮状态变化
            }

            @Override
            public void onError(String error) {
                LogUtils.eTag(TAG, "语音识别错误: " + error);
                Toast.makeText(ChatActivity.this, getString(R.string.toast_voice_recognition_error, error), Toast.LENGTH_SHORT).show();
            }

            // 新增：语音转文字回调方法
            @Override
            public void onVoiceToTextStart() {
                LogUtils.dTag(TAG, "语音转文字开始");
                // 可以在这里显示转换进度提示
            }

            @Override
            public void onVoiceToTextProgress(String progress) {
                LogUtils.dTag(TAG, "语音转文字进度: " + progress);
                // 可以实时显示转换进度
            }

            @Override
            public void onVoiceToTextComplete(String text) {
                LogUtils.dTag(TAG, "语音转文字完成: " + text);
                handleVoiceToTextResult(text, true);
            }

            @Override
            public void onVoiceToTextError(String error) {
                LogUtils.eTag(TAG, "语音转文字失败: " + error);
                handleVoiceToTextResult(error, false);
            }
        }, VoiceUsageType.VOICE_CHAT);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setupClickListeners() {
        // 语音/键盘切换按钮
        binding.btnVoiceSwitch.setOnClickListener(v -> toggleInputMode());

        // 发送按钮（仅用于键盘输入）
        binding.btnSend.setOnClickListener(v -> {
            String message = binding.etMessage.getText().toString().trim();
            if (!TextUtils.isEmpty(message)) {
                // 播放点击动画
                playButtonClickAnimation(binding.btnSend);

                // 发送消息
                sendTextMessage(message);

                // 清空输入框
                binding.etMessage.setText("");
                viewModel.clearMessage();
            }
        });

        // 语音按住按钮的触摸事件 - 委托给VoiceRecognitionExecutor
        binding.btnVoiceHold.setOnTouchListener((v, event) -> {
            if (voiceExecutor != null) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 停止当前播放的语音（避免音频设备冲突）
                        if (currentPlayingMessageId != null) {
                            LogUtils.dTag(TAG, "停止当前播放的语音，准备开始录音");
                            stopCurrentPlaying();

                            // 额外等待一小段时间确保音频设备完全释放
                            handler.postDelayed(() -> {
                                if (voiceExecutor != null) {
                                    voiceExecutor.handleTouchDown(event);
                                }
                            }, 100);
                        } else {
                            voiceExecutor.handleTouchDown(event);
                        }
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        voiceExecutor.handleTouchMove(event);
                        return true;

                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        voiceExecutor.handleTouchUp();
                        return true;
                }
            }
            return false;
        });
    }

    private void setupTextWatcher() {
        binding.etMessage.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 更新ViewModel
                viewModel.setCurrentMessage(s.toString());
                // 根据输入内容启用/禁用发送按钮
                updateSendButtonState(!TextUtils.isEmpty(s.toString().trim()));
            }
        });
    }

    /**
     * 切换输入模式（键盘/语音）
     */
    private void toggleInputMode() {
        // 使用ViewModel管理状态
        viewModel.toggleInputMode();

        boolean isVoiceMode = viewModel.isVoiceMode();

        if (isVoiceMode) {
            // 切换到语音模式
            binding.etMessage.setVisibility(View.GONE);
            binding.btnVoiceHold.setVisibility(View.VISIBLE);
            binding.btnVoiceSwitch.setImageResource(R.drawable.ic_keyboard);
            updateSendButtonState(false);
        } else {
            // 切换到键盘模式
            binding.etMessage.setVisibility(View.VISIBLE);
            binding.btnVoiceHold.setVisibility(View.GONE);
            binding.btnVoiceSwitch.setImageResource(R.drawable.ic_voice);

            // 重新检查发送按钮状态
            boolean hasText = !TextUtils.isEmpty(binding.etMessage.getText().toString().trim());
            updateSendButtonState(hasText);
        }
    }

    /**
     * 发送文字消息
     */
    private void sendTextMessage(String text) {
        if (TextUtils.isEmpty(text)) return;

        LogUtils.dTag(TAG, "发送文字消息: " + text);
        ChatMessage message = new ChatMessage(text, ChatMessage.TYPE_TEXT);
        chatMessages.add(message);
        chatAdapter.notifyItemInserted(chatMessages.size() - 1);
        binding.rvChatMessages.scrollToPosition(chatMessages.size() - 1);
    }

    /**
     * 发送语音消息
     */
    private void sendVoiceMessage(byte[] audioData) {
        if (audioData == null || audioData.length == 0) {
            LogUtils.eTag(TAG, "音频数据为空，无法发送语音消息");
            return;
        }

        LogUtils.dTag(TAG, "发送语音消息，数据大小: " + audioData.length );

        // 保存音频文件
        String audioFilePath = saveAudioToFile(audioData);
        if (audioFilePath == null) {
            Toast.makeText(this, R.string.toast_save_voice_failed, Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建语音消息
        ChatMessage message = new ChatMessage("", ChatMessage.TYPE_VOICE);
        message.setAudioFilePath(audioFilePath);
        message.setAudioData(audioData);

        // 计算音频时长（返回毫秒）
        long durationMs = calculateAudioDuration(audioData);
        message.setDuration(durationMs);

        // 添加到聊天列表
        chatMessages.add(message);
        chatAdapter.notifyItemInserted(chatMessages.size() - 1);
        binding.rvChatMessages.scrollToPosition(chatMessages.size() - 1);

        LogUtils.dTag(TAG, "语音消息已添加到列表");
    }

    /**
     * 保存音频数据到文件
     */
    private String saveAudioToFile(byte[] audioData) {
        try {
            // 创建音频文件
            File audioDir = new File(getCacheDir(), "voice");
            boolean exists = audioDir.exists();
            if (!exists) {
                exists = audioDir.mkdirs();
            }
            if (exists) {
                String fileName = "voice_" + System.currentTimeMillis() + ".wav";
                File audioFile = new File(audioDir, fileName);

                // 写入文件
                FileOutputStream fos = new FileOutputStream(audioFile);
                fos.write(audioData);
                fos.close();

                LogUtils.dTag(TAG, "音频已保存到: " + audioFile.getAbsolutePath());
                return audioFile.getAbsolutePath();
            }
            return null;
        } catch (IOException e) {
            LogUtils.eTag(TAG, "保存音频文件失败", e);
            return null;
        }
    }

    /**
     * 计算音频时长（返回毫秒）
     */
    private long calculateAudioDuration(byte[] audioData) {
        // WAV格式：44字节头部，16000Hz采样率，16位单声道
        if (audioData.length > 44) {
            int pcmLength = audioData.length - 44;  // 去掉WAV头部
            // 16000Hz采样率，16位(2字节)，单声道
            // 时长(秒) = PCM数据字节数 / (采样率 * 字节数/采样点)
            double durationSeconds = (double) pcmLength / (16000.0 * 2.0);
            long durationMs = (long) Math.ceil(durationSeconds * 1000); // 转换为毫秒并向上取整

            LogUtils.dTag(TAG, "音频时长计算: PCM长度=" + pcmLength + "字节, 时长=" + durationSeconds + "秒, 毫秒=" + durationMs + "ms");

            return Math.max(1000, durationMs); // 至少显示1秒(1000毫秒)
        }
        return 1000; // 默认1秒
    }

    /**
     * 播放语音消息
     */
    private void playVoiceMessage(ChatMessage message) {
        if (message.getType() != ChatMessage.TYPE_VOICE) {
            LogUtils.wTag(TAG, "不是语音消息，无法播放");
            return;
        }

        LogUtils.dTag(TAG, "点击播放语音消息: " + message.getId());

        // 如果正在播放相同的消息，则停止播放
        if (message.getId().equals(currentPlayingMessageId)) {
            LogUtils.dTag(TAG, "停止当前播放的语音");
            stopCurrentPlaying();
            return;
        }

        // 停止当前正在播放的语音
        stopCurrentPlaying();

        // 开始播放新的语音
        currentPlayingMessageId = message.getId();
        message.setPlaying(true);
        chatAdapter.updatePlayingState(message.getId(), true);

        LogUtils.dTag(TAG, "开始播放语音，消息ID: " + message.getId());

        // 优先使用内存中的音频数据
        if (message.getAudioData() != null) {
            LogUtils.dTag(TAG, "使用内存数据播放");
            playWavDataFromMemory(message.getAudioData());
        } else if (message.getAudioFilePath() != null) {
            LogUtils.dTag(TAG, "使用文件播放: " + message.getAudioFilePath());
            playVoiceFromFile(message.getAudioFilePath());
        } else {
            LogUtils.wTag(TAG, "语音数据不可用");
            Toast.makeText(this, R.string.toast_voice_data_unavailable, Toast.LENGTH_SHORT).show();
            stopCurrentPlaying();
        }
    }

    /**
     * 停止当前播放的语音
     */
    private void stopCurrentPlaying() {
        LogUtils.dTag(TAG, "停止当前播放");

        // 停止MediaPlayer
        if (currentMediaPlayer != null) {
            try {
                if (currentMediaPlayer.isPlaying()) {
                    currentMediaPlayer.stop();
                }
                currentMediaPlayer.release();
                LogUtils.dTag(TAG, "MediaPlayer已停止并释放");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "停止MediaPlayer时出错", e);
            }
            currentMediaPlayer = null;
        }

        // 停止AudioTrack
        if (currentAudioTrack != null) {
            try {
                if (currentAudioTrack.getState() == AudioTrack.STATE_INITIALIZED) {
                    if (currentAudioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                        currentAudioTrack.pause();
                        currentAudioTrack.flush();
                    }
                    currentAudioTrack.stop();
                }
                currentAudioTrack.release();
                LogUtils.dTag(TAG, "AudioTrack已停止并释放");
            } catch (Exception e) {
                LogUtils.eTag(TAG, "停止AudioTrack时出错", e);
            }
            currentAudioTrack = null;
        }

        // 中断AudioTrack播放线程
        if (audioTrackThread != null && audioTrackThread.isAlive()) {
            audioTrackThread.interrupt();
            try {
                audioTrackThread.join(500); // 等待最多500ms
            } catch (InterruptedException e) {
                LogUtils.dTag(TAG, "等待AudioTrack线程结束时被中断");
            }
            audioTrackThread = null;
            LogUtils.dTag(TAG, "AudioTrack播放线程已停止");
        }

        // 更新UI状态
        if (currentPlayingMessageId != null) {
            chatAdapter.updatePlayingState(currentPlayingMessageId, false);
            currentPlayingMessageId = null;
            LogUtils.dTag(TAG, "播放状态已清除");
        }

        // 强制释放音频焦点（如果有的话）
        try {
            android.media.AudioManager audioManager = (android.media.AudioManager) getSystemService(AUDIO_SERVICE);
            if (audioManager != null) {
                // 这里可以添加音频焦点释放的代码，如果之前申请了的话
                LogUtils.dTag(TAG, "音频管理器状态检查完成");
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "音频管理器操作失败", e);
        }
    }

    /**
     * 播放完成回调
     */
    private void onPlaybackComplete() {
        LogUtils.dTag(TAG, "播放完成");
        // 播放完成后，只有在必要时才重新初始化语音识别
        // 通常播放完成后不需要重新初始化，除非有明确的冲突
        runOnUiThread(this::stopCurrentPlaying);
    }

    /**
     * 从文件播放语音
     */
    private void playVoiceFromFile(String audioFilePath) {
        try {
            File audioFile = new File(audioFilePath);
            if (!audioFile.exists()) {
                Toast.makeText(this, R.string.toast_voice_file_not_found, Toast.LENGTH_SHORT).show();
                onPlaybackComplete();
                return;
            }

            // 创建新的MediaPlayer
            currentMediaPlayer = new MediaPlayer();
            currentMediaPlayer.setDataSource(audioFilePath);
            currentMediaPlayer.prepareAsync();

            currentMediaPlayer.setOnPreparedListener(mp -> {
                LogUtils.dTag(TAG, "开始播放语音文件");
                mp.start();
            });

            currentMediaPlayer.setOnCompletionListener(mp -> {
                LogUtils.dTag(TAG, "语音播放完成");
                onPlaybackComplete();
            });

            currentMediaPlayer.setOnErrorListener((mp, what, extra) -> {
                LogUtils.eTag(TAG, "播放语音出错: what=" + what + ", extra=" + extra);
                Toast.makeText(this, R.string.toast_playback_failed, Toast.LENGTH_SHORT).show();
                onPlaybackComplete();
                return true;
            });

        } catch (Exception e) {
            LogUtils.eTag(TAG, "播放语音失败", e);
            Toast.makeText(this, getString(R.string.toast_playback_failed_with_message, e.getMessage()), Toast.LENGTH_SHORT).show();
            onPlaybackComplete();
        }
    }

    /**
     * 从内存播放WAV数据
     *
     * @noinspection deprecation
     */
    @SuppressLint("ObsoleteSdkInt")
    private void playWavDataFromMemory(byte[] wavData) {
        try {
            // 验证WAV数据
            if (wavData.length < 44) {
                Toast.makeText(this, R.string.toast_invalid_voice_data, Toast.LENGTH_SHORT).show();
                onPlaybackComplete();
                return;
            }

            // 提取PCM数据
            byte[] pcmData = new byte[wavData.length - 44];
            System.arraycopy(wavData, 44, pcmData, 0, pcmData.length);

            // 使用AudioTrack播放
            int sampleRate = 16000;
            int channelConfig = android.media.AudioFormat.CHANNEL_OUT_MONO;
            int audioFormat = android.media.AudioFormat.ENCODING_PCM_16BIT;
            int bufferSize = AudioTrack.getMinBufferSize(
                    sampleRate, channelConfig, audioFormat);

            // 使用新的AudioTrack.Builder API (API 23+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                android.media.AudioAttributes audioAttributes = new android.media.AudioAttributes.Builder()
                        .setUsage(android.media.AudioAttributes.USAGE_MEDIA)
                        .setContentType(android.media.AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build();

                android.media.AudioFormat audioFormatBuilder = new android.media.AudioFormat.Builder()
                        .setSampleRate(sampleRate)
                        .setEncoding(audioFormat)
                        .setChannelMask(channelConfig)
                        .build();

                currentAudioTrack = new AudioTrack.Builder()
                        .setAudioAttributes(audioAttributes)
                        .setAudioFormat(audioFormatBuilder)
                        .setBufferSizeInBytes(Math.max(bufferSize, pcmData.length))
                        .setTransferMode(AudioTrack.MODE_STATIC)
                        .build();
            } else {
                // 兼容旧版本 (API < 23)
                currentAudioTrack = new AudioTrack(
                        android.media.AudioManager.STREAM_MUSIC,
                        sampleRate, channelConfig, audioFormat,
                        Math.max(bufferSize, pcmData.length),
                        AudioTrack.MODE_STATIC);
            }

            currentAudioTrack.write(pcmData, 0, pcmData.length);
            currentAudioTrack.play();

            LogUtils.dTag(TAG, "开始内存模式播放，数据大小: " + pcmData.length);

            // 在后台线程等待播放完成
            audioTrackThread = new Thread(() -> {
                try {
                    long duration = (pcmData.length * 1000L) / (sampleRate * 2);
                    Thread.sleep(duration);

                    // 播放完成
                    runOnUiThread(this::onPlaybackComplete);

                } catch (InterruptedException e) {
                    LogUtils.dTag(TAG, "AudioTrack播放线程被中断");
                    // 线程被中断，不需要回调完成
                }
            });
            audioTrackThread.start();

        } catch (Exception e) {
            LogUtils.eTag(TAG, "内存播放失败", e);
            Toast.makeText(this, R.string.toast_playback_failed, Toast.LENGTH_SHORT).show();
            onPlaybackComplete();
        }
    }

    /**
     * 更新发送按钮状态，无动画效果
     */
    private void updateSendButtonState(boolean enabled) {
        if (binding.btnSend.isEnabled() != enabled) {
            binding.btnSend.setEnabled(enabled);

            if (enabled) {
                // 启用状态：正常大小，完全不透明
                binding.btnSend.setAlpha(1.0f);
                binding.btnSend.setScaleX(1.0f);
                binding.btnSend.setScaleY(1.0f);
            } else {
                // 禁用状态：缩小，半透明
                binding.btnSend.setAlpha(0.5f);
                binding.btnSend.setScaleX(0.90f);
                binding.btnSend.setScaleY(0.90f);
            }
        }
    }

    /**
     * 长按语音消息转文字
     */
    private void convertVoiceToText(ChatMessage message) {
        Log.d(TAG, "长按语音消息，开始转文字: " + message.getId());

        // 如果已经有转换结果，直接显示/隐藏
        if (message.hasConvertedText()) {
            message.setShowConvertedText(!message.isShowConvertedText());
            chatAdapter.notifyItemChanged(getMessagePosition(message));
            return;
        }

        // 如果正在转换中，不重复转换
        if (message.isConverting()) {
            Toast.makeText(this, R.string.toast_voice_to_text_converting, Toast.LENGTH_SHORT).show();
            return;
        }

        // 检查是否有音频数据
        if (message.getAudioData() == null && message.getAudioFilePath() == null) {
            Toast.makeText(this, R.string.toast_voice_data_unavailable, Toast.LENGTH_SHORT).show();
            return;
        }

        // 停止当前播放
        stopCurrentPlaying();

        message.setConverting(true);
        chatAdapter.notifyItemChanged(getMessagePosition(message));

        // 使用VoiceRecognitionExecutor进行转换
        if (voiceExecutor != null) {
            // 记录正在转换的消息ID
            convertingMessageId = message.getId();
            if (message.getAudioData() != null) {
                // 优先使用内存中的音频数据
                voiceExecutor.convertVoiceToText(message.getAudioData());
            } else if (message.getAudioFilePath() != null) {
                // 使用音频文件
                voiceExecutor.convertVoiceToText(message.getAudioFilePath());
            }
        }
    }

    /**
     * 处理语音转文字结果
     *
     * @param result    转换结果文本或错误信息
     * @param isSuccess 是否转换成功
     */
    private void handleVoiceToTextResult(String result, boolean isSuccess) {
        if (convertingMessageId != null) {
            try {
                ChatMessage message = findMessageById(convertingMessageId);
                if (message != null) {
                    message.setConverting(false);

                    if (isSuccess && !TextUtils.isEmpty(result)) {
                        message.setConvertedText(result);
                        message.setShowConvertedText(true);
                    } else {
                        if (isSuccess) {
                            Toast.makeText(this, R.string.toast_no_text_content, Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(this, getString(R.string.toast_voice_to_text_failed, result), Toast.LENGTH_SHORT).show();
                        }
                    }

                    chatAdapter.notifyItemChanged(getMessagePosition(message));
                }
            } catch (Exception e) {
                LogUtils.eTag(TAG, "处理语音转文字结果失败", e);
            } finally {
                // 清理状态
                convertingMessageId = null;
            }
        }

    }

    /**
     * 根据ID查找消息
     */
    private ChatMessage findMessageById(String messageId) {
        if (TextUtils.isEmpty(messageId)) return null;

        for (ChatMessage message : chatMessages) {
            if (messageId.equals(message.getId())) {
                return message;
            }
        }
        return null;
    }

    /**
     * 获取消息在列表中的位置
     */
    private int getMessagePosition(ChatMessage targetMessage) {
        if (targetMessage == null) return -1;

        for (int i = 0; i < chatMessages.size(); i++) {
            if (targetMessage.getId().equals(chatMessages.get(i).getId())) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 播放按钮点击动画
     */
    private void playButtonClickAnimation(View button) {
        button.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(()
                        -> button.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start())
                .start();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 停止语音播放
        stopCurrentPlaying();

        // 清理VoiceRecognitionExecutor
        if (voiceExecutor != null) {
            voiceExecutor.cleanup();
            voiceExecutor = null;
            LogUtils.dTag(TAG, "VoiceRecognitionExecutor已清理");
        }
        // 清理声音资源
        if (soundPool != null) {
            soundPool.release();
            soundPool = null;
        }

        // 清理DataBinding
        binding = null;
        viewModel = null;

        LogUtils.dTag(TAG, "ChatActivity销毁，资源已清理");
    }
}
