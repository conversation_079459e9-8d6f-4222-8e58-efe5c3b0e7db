package com.supreme.smart.sewing.push;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;

/**
 * 网络状态监听器
 * 负责监听网络状态变化并通知回调
 */
public class NetworkStateMonitor {
    
    private static final String TAG = "NetworkStateMonitor";
    
    private ConnectivityManager connectivityManager;
    private ConnectivityManager.NetworkCallback networkCallback;

    // 状态缓存，避免重复通知
    private volatile boolean lastInternetState = false;
    private volatile long lastCapabilityChangeTime = 0;
    private static final long CAPABILITY_CHANGE_DEBOUNCE_MS = 3000; // 3秒防抖

    // 网络断开保护期
    private volatile long networkLostTime = 0;
    private static final long NETWORK_LOST_PROTECTION_MS = 5000; // 5秒保护期

    // 网络状态回调接口
    public interface NetworkStateCallback {
        void onNetworkAvailable();
        void onNetworkLost();
        void onNetworkCapabilitiesChanged(boolean hasInternet);
    }

    private NetworkStateCallback callback;

    // 电源优化相关
    private PowerOptimizationConfig powerConfig;
    private android.os.Handler debounceHandler;
    private Runnable pendingNetworkAvailableCallback;
    private Runnable pendingNetworkLostCallback;
    
    public NetworkStateMonitor(Context context) {
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        powerConfig = PowerOptimizationConfig.getInstance(context);
        debounceHandler = new android.os.Handler(android.os.Looper.getMainLooper());

        // 初始化当前网络状态
        initializeCurrentNetworkState();
    }

    /**
     * 初始化当前网络状态
     */
    private void initializeCurrentNetworkState() {
        try {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork != null) {
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
                if (capabilities != null) {
                    lastInternetState = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                                       capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                    LogUtils.dTag(TAG, "初始化网络状态: " + (lastInternetState ? "有互联网连接" : "无互联网连接"));
                }
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化网络状态失败", e);
            lastInternetState = false;
        }
    }
    
    /**
     * 设置网络状态回调
     */
    public void setNetworkStateCallback(NetworkStateCallback callback) {
        this.callback = callback;
    }
    
    /**
     * 开始监听网络状态
     */
    public void startMonitoring() {
        if (connectivityManager == null) {
            LogUtils.eTag(TAG, "ConnectivityManager未初始化");
            return;
        }
        
        networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                super.onAvailable(network);
                LogUtils.dTag(TAG, "网络状态变化: 已连接");

                // 检查网络是否真正可用（有互联网连接）
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
                LogUtils.dTag(TAG, "网络能力检查: capabilities=" + (capabilities != null));

                if (capabilities != null) {
                    boolean hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
                    boolean isValidated = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                    LogUtils.dTag(TAG, "网络能力详情: hasInternet=" + hasInternet + ", isValidated=" + isValidated);

                    if (hasInternet && isValidated) {
                        LogUtils.dTag(TAG, "网络完全可用，触发网络恢复回调");
                        // 使用防抖机制
                        scheduleNetworkAvailableCallback();
                    } else if (hasInternet) {
                        // 有网络但未验证，也触发回调（可能是验证延迟）
                        LogUtils.dTag(TAG, "网络可用但未验证，仍触发网络恢复回调");
                        scheduleNetworkAvailableCallback();
                    } else {
                        LogUtils.wTag(TAG, "网络连接但无互联网能力，跳过回调");
                    }
                } else {
                    LogUtils.wTag(TAG, "无法获取网络能力信息，跳过回调");
                }
            }
            
            @Override
            public void onLost(@NonNull Network network) {
                super.onLost(network);
                LogUtils.dTag(TAG, "网络状态变化: 已断开");

                // 使用防抖机制
                scheduleNetworkLostCallback();
            }
            
            @Override
            public void onCapabilitiesChanged(@NonNull Network network,
                                              @NonNull NetworkCapabilities networkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities);

                long currentTime = System.currentTimeMillis();
                boolean hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);

                // 防抖控制：避免频繁的能力变化通知
                if (currentTime - lastCapabilityChangeTime < CAPABILITY_CHANGE_DEBOUNCE_MS) {
                    LogUtils.dTag(TAG, "跳过网络能力变化通知（防抖中），距离上次仅 " +
                                 (currentTime - lastCapabilityChangeTime) + "ms");
                    return;
                }

                // 状态缓存：只在状态真正改变时才通知
                if (hasInternet != lastInternetState) {
                    lastInternetState = hasInternet;
                    lastCapabilityChangeTime = currentTime;

                    LogUtils.iTag(TAG, "网络能力状态变化: " + (hasInternet ? "有互联网连接" : "无互联网连接"));

                    if (callback != null) {
                        callback.onNetworkCapabilitiesChanged(hasInternet);
                    }
                }
            }
        };
        
        // 创建网络请求，监听所有网络变化
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        NetworkRequest networkRequest = builder.build();
        
        try {
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback);
            LogUtils.dTag(TAG, "网络状态监听注册成功");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "注册网络状态监听失败", e);
        }
    }
    
    /**
     * 停止监听网络状态
     */
    public void stopMonitoring() {
        if (networkCallback != null && connectivityManager != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
                networkCallback = null;
                LogUtils.dTag(TAG, "网络状态监听注销成功");
            } catch (IllegalArgumentException e) {
                LogUtils.wTag(TAG, "网络监听器未注册或已注销");
            }
        }
    }

    /**
     * 调度网络可用回调（带防抖）
     */
    private void scheduleNetworkAvailableCallback() {
        // 取消之前的回调
        if (pendingNetworkAvailableCallback != null) {
            debounceHandler.removeCallbacks(pendingNetworkAvailableCallback);
        }

        // 创建新的回调
        pendingNetworkAvailableCallback = () -> {
            if (callback != null) {
                LogUtils.iTag(TAG, "🌐 执行网络可用回调（防抖后）");
                callback.onNetworkAvailable();
            } else {
                LogUtils.wTag(TAG, "网络回调为空，无法执行");
            }
        };

        // 延迟执行
        long debounceMs = powerConfig.getNetworkConfig().networkStateDebounceMs;
        debounceHandler.postDelayed(pendingNetworkAvailableCallback, debounceMs);
        LogUtils.dTag(TAG, "网络可用回调已调度，防抖时间: " + debounceMs + "ms");
    }

    /**
     * 调度网络断开回调（带防抖）
     */
    private void scheduleNetworkLostCallback() {
        // 取消之前的回调
        if (pendingNetworkLostCallback != null) {
            debounceHandler.removeCallbacks(pendingNetworkLostCallback);
        }

        // 创建新的回调
        pendingNetworkLostCallback = () -> {
            if (callback != null) {
                LogUtils.dTag(TAG, "执行网络断开回调（防抖后）");
                callback.onNetworkLost();
            }
        };

        // 延迟执行
        long debounceMs = powerConfig.getNetworkConfig().networkStateDebounceMs;
        debounceHandler.postDelayed(pendingNetworkLostCallback, debounceMs);
        LogUtils.dTag(TAG, "网络断开回调已调度，防抖时间: " + debounceMs + "ms");
    }

    /**
     * 释放资源
     */
    public void release() {
        stopMonitoring();

        // 清理防抖回调
        if (debounceHandler != null) {
            if (pendingNetworkAvailableCallback != null) {
                debounceHandler.removeCallbacks(pendingNetworkAvailableCallback);
            }
            if (pendingNetworkLostCallback != null) {
                debounceHandler.removeCallbacks(pendingNetworkLostCallback);
            }
        }

        callback = null;
        connectivityManager = null;
        powerConfig = null;
        debounceHandler = null;
        pendingNetworkAvailableCallback = null;
        pendingNetworkLostCallback = null;
    }
}
