package com.supreme.smart.sewing.application;

import android.app.Application;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.camera.camera2.Camera2Config;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.CameraXConfig;

import com.blankj.utilcode.util.LogUtils;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.LogLvl;
import com.jakewharton.processphoenix.ProcessPhoenix;
import com.supreme.smart.sewing.database.config.RealmConfig;
import com.supreme.smart.sewing.database.utils.DataActionUtils;
import com.supreme.smart.sewing.push.PowerOptimizationConfig;
import com.supreme.smart.sewing.push.PushManager;
import com.supreme.smart.sewing.utils.CommonUtils;
import com.supreme.smart.sewing.utils.MLKitManager;
import com.supreme.smart.sewing.voice.ability.AbilityManager;

import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeoutException;

import cn.hutool.core.io.FileUtil;
import io.reactivex.rxjava3.exceptions.UndeliverableException;
import io.reactivex.rxjava3.plugins.RxJavaPlugins;
import io.realm.Realm;
import io.realm.RealmConfiguration;
import xcrash.ICrashCallback;
import xcrash.XCrash;

/**
 * 智能缝纫系统应用程序类
 */
public class SmartSewingApplication extends Application implements CameraXConfig.Provider {
    private static final String TAG = "SmartSewingApplication";
    private static volatile SmartSewingApplication instance = null;
    private volatile MediaPlayer mediaPlayer;

    @Override
    public void onCreate() {
        super.onCreate();
        Realm.init(this);
        instance = this;

        // installFileLog();
        RealmConfiguration config = RealmConfig.getDefaultConfig();
        Realm.setDefaultConfiguration(config);
        String actionId = CommonUtils.getCurrentDateString();
        DataActionUtils.savePowerAction(actionId, true);
        initLogConfig();
        initCrashDumper();

        // 设置RxJava全局错误处理器
        setupRxJavaErrorHandler();
        // 在后台线程预加载MLKit人脸检测器
        preloadMLKitModels();

        // 设置讯飞日志输出
        AiHelper.getInst().setLogInfo(LogLvl.VERBOSE, 2, getExternalCacheDir() + "/iflytekAikit/aeeLog.txt");
        // 初始化科大讯飞SDK
        AbilityManager.getInstance().initializeSdk(this);

        initializePushService();
        LogUtils.dTag(TAG, "系统启动！");
    }

    public static SmartSewingApplication getInstance() {
        return instance;
    }


    /**
     * 初始化推送服务
     */
    private void initializePushService() {
        try {
            PushManager.getInstance(this).startPushService();
            LogUtils.dTag(TAG, "MQTT推送服务初始化成功");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "MQTT推送服务初始化失败: " + e.getMessage());
        }
    }


    /**
     * 系統日志配置
     */
    private void initLogConfig() {
        String logDir = Environment.getExternalStorageDirectory() + "/SUPREME/sewing/log";
        if (!FileUtil.exist(logDir)) {
            FileUtil.mkdir(logDir);
        } else {
            if (FileUtil.isFile(logDir)) {
                FileUtil.del(logDir);
                FileUtil.mkdir(logDir);
            }
        }


        LogUtils.getConfig()
                .setLogSwitch(true) // 设置log总开关，包括输出到控制台和文件，默认开
                .setConsoleSwitch(true) // 设置是否输出到控制台开关，默认开
                .setGlobalTag(null) // 设置log全局标签，默认为空，
                .setLog2FileSwitch(false) // 打印log时是否存到文件的开关，默认关
                .setDir(logDir) // 当自定义路径为空时，写入应用的/cache/log/目录中
                .setFilePrefix("sewing-sys-log") // 当文件前缀为空时，默认为"util"，即写入文件为"util-MM-dd.txt"
                .setLogHeadSwitch(true) // 设置log头信息开关，默认为开
                .setBorderSwitch(false) // 输出日志是否带边框开关，默认开
                .setConsoleFilter(LogUtils.V) // log的控制台过滤器，和logcat过滤器同理，默认Verbose
                .setFileFilter(LogUtils.V) // log文件过滤器，和logcat过滤器同理，默认Verbose
                .setStackDeep(1) // log栈深度，默认为1
                .setSaveDays(7); // log文件保留7天
    }

    private void initCrashDumper() {
        String crashLogDir = Environment.getExternalStorageDirectory() + "/SUPREME/sewing/crash-log";
        XCrash.InitParameters initParameters = new XCrash.InitParameters();
        File dir = new File(crashLogDir);

        if (FileUtil.isFile(dir)) {
            FileUtil.del(dir);
        }

        if (!FileUtil.exist(dir)) {
            FileUtil.mkdir(dir);
        }

        initParameters.setLogDir(crashLogDir);
        initParameters.setJavaCallback(myCrashCallback);
        initParameters.setNativeCallback(myCrashCallback);
        initParameters.setAnrCallback(myCrashCallback);
        XCrash.init(this, initParameters);
    }


    private final ICrashCallback myCrashCallback = (logPath, emergency) -> {
        LogUtils.eTag(TAG, "APP崩溃了");

        ProcessPhoenix.triggerRebirth(instance);
    };


    /**
     * 设置RxJava全局错误处理器，处理UndeliverableException
     */
    private void setupRxJavaErrorHandler() {
        RxJavaPlugins.setErrorHandler(e -> {
            if (e instanceof UndeliverableException) {
                e = e.getCause();
            }

            assert e != null;

            if (e instanceof IOException) {
                // 网络异常，通常可以忽略
                LogUtils.wTag(TAG, "网络异常被忽略: " + e.getMessage());
                return;
            }

            if (e instanceof InterruptedException) {
                // 线程中断，通常可以忽略
                LogUtils.wTag(TAG, "线程中断被忽略: " + e.getMessage());
                return;
            }

            if ((e instanceof NullPointerException) || (e instanceof IllegalArgumentException)) {
                // 可能是编程错误，应该崩溃
                LogUtils.eTag(TAG, "严重错误，应用将崩溃", Log.getStackTraceString(e));
                Objects.requireNonNull(Thread.currentThread().getUncaughtExceptionHandler())
                        .uncaughtException(Thread.currentThread(), e);
                return;
            }

            if (e instanceof IllegalStateException) {
                // 状态异常，通常可以忽略
                LogUtils.wTag(TAG, "状态异常被忽略: " + e.getMessage());
                return;
            }

            if (e instanceof TimeoutException) {
                // 超时异常，记录但不崩溃
                LogUtils.wTag(TAG, "超时异常被忽略: " + e.getMessage());
                return;
            }

            // 其他未知异常
            LogUtils.wTag(TAG, "未知异常被忽略: " + e.getMessage(), e);
        });
    }

    /**
     * 预加载MLKit模型，避免首次使用时的延迟
     */
    private void preloadMLKitModels() {
        new Thread(() -> {
            try {
                LogUtils.iTag(TAG, "开始预加载人脸检测模型");
                long startTime = System.currentTimeMillis();

                // 使用MLKitManager预加载人脸检测器
                MLKitManager.getInstance().getFaceDetector();

                long endTime = System.currentTimeMillis();
                LogUtils.iTag(TAG, "人脸检测模型预加载完成，耗时: " + (endTime - startTime) + "ms");

            } catch (Exception e) {
                LogUtils.eTag(TAG, "预加载MLKit模型失败", Log.getStackTraceString(e));
            }
        }).start();
    }

    @NonNull
    @Override
    public CameraXConfig getCameraXConfig() {
        return CameraXConfig.Builder.fromConfig(Camera2Config.defaultConfig())
                // 限制CameraX只使用前置摄像头，忽略后置摄像头
                .setAvailableCamerasLimiter(CameraSelector.DEFAULT_FRONT_CAMERA)
                .build();
    }

    /**
     * 播放音频文件
     *
     * @param fileName 音频文件名
     */
    public void playAudio(String fileName) {
        if (mediaPlayer == null) {
            synchronized (SmartSewingApplication.class) {
                if (mediaPlayer == null)
                    mediaPlayer = new MediaPlayer();
            }
        }


        synchronized (SmartSewingApplication.class) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }

                mediaPlayer.reset(); // 重置 MediaPlayer 到 IDLE 状态
                AssetFileDescriptor afd = getAssets().openFd(fileName);
                mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                afd.close(); // 完成后关闭 AssetFileDescriptor

                mediaPlayer.prepare();
                mediaPlayer.start();
            } catch (Exception e) {
                LogUtils.eTag(TAG, Log.getStackTraceString(e));
            }
        }
    }

    /**
     * 播放警报音
     */
    public void soundAlarm() {
        Uri uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        RingtoneManager.getRingtone(getApplicationContext(), uri).play();
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        LogUtils.iTag(TAG, "应用程序正在终止，开始清理资源");

        // 停止推送服务
        try {
            PushManager.getInstance(this).stopPushService();
            LogUtils.dTag(TAG, "推送服务已停止");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "停止推送服务失败: " + e.getMessage());
        }

        // 释放电源优化配置资源
        try {
            PowerOptimizationConfig.getInstance(this).release();
            LogUtils.dTag(TAG, "电源优化配置资源已释放");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "释放电源优化配置失败: " + e.getMessage());
        }

        // 释放MLKit资源
        try {
            MLKitManager.getInstance().release();
            LogUtils.dTag(TAG, "MLKit资源已释放");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "释放MLKit资源失败: " + e.getMessage());
        }

        LogUtils.iTag(TAG, "应用程序资源清理完成");
    }
}