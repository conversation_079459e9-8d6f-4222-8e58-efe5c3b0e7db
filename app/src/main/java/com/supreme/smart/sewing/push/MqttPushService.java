package com.supreme.smart.sewing.push;

import android.app.Notification;
import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MQTT推送服务
 */
public class MqttPushService extends Service {

    private static final String TAG = "MqttPushService";

    // 组件管理器
    private MqttConnectionManager mqttConnectionManager;
    private NetworkStateMonitor networkStateMonitor;
    private PushNotificationManager notificationManager;

    // 线程管理
    private ExecutorService executorService;
    private Handler mainHandler;

    // 电源优化配置
    private PowerOptimizationConfig powerConfig;

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.dTag(TAG, "MQTT推送服务创建");

        // 初始化电源优化配置
        powerConfig = PowerOptimizationConfig.getInstance(this);
        LogUtils.iTag(TAG, "设备类型: " + powerConfig.getDeviceType());

        // 初始化线程管理（使用电源优化配置）
        initializeThreadManagement();
        mainHandler = new Handler(Looper.getMainLooper());

        // 初始化各个管理器
        initializeManagers();

        // 初始化MQTT连接
        initMqttConnection();
    }

    /**
     * 初始化线程管理（使用电源优化配置）
     */
    private void initializeThreadManagement() {
        PowerOptimizationConfig.ServiceConfig serviceConfig = powerConfig.getServiceConfig();

        if (serviceConfig.useFixedThreadPool) {
            executorService = Executors.newFixedThreadPool(serviceConfig.threadPoolSize);
            LogUtils.dTag(TAG, "使用固定线程池，大小: " + serviceConfig.threadPoolSize);
        } else {
            executorService = Executors.newCachedThreadPool();
            LogUtils.dTag(TAG, "使用缓存线程池");
        }
    }

    /**
     * 初始化各个管理器
     */
    private void initializeManagers() {
        // 初始化通知管理器
        notificationManager = new PushNotificationManager(this);

        // 初始化网络状态监听器
        networkStateMonitor = new NetworkStateMonitor(this);
        networkStateMonitor.setNetworkStateCallback(new NetworkStateCallback());

        // 初始化MQTT连接管理器
        mqttConnectionManager = new MqttConnectionManager();
        mqttConnectionManager.setConnectionCallback(new MqttConnectionCallback());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogUtils.dTag(TAG, "MQTT推送服务启动 - flags: " + flags + ", startId: " + startId);

        // 启动前台服务
        startForegroundService();

        // 启动网络监听
        startNetworkMonitoring();

        // 启动电源状态监控
        startPowerMonitoring();

        // 返回START_STICKY确保服务被杀死后自动重启
        // START_REDELIVER_INTENT会重新传递Intent
        return START_STICKY;
    }

    private void startNetworkMonitoring() {
        networkStateMonitor.startMonitoring();
    }

    /**
     * 启动电源状态监控
     */
    private void startPowerMonitoring() {
        if (powerConfig != null) {
            powerConfig.startMonitoring();
            LogUtils.iTag(TAG, "电源状态监控已启动");
        } else {
            LogUtils.wTag(TAG, "电源配置为空，无法启动电源监控");
        }
    }

    /**
     * 启动前台服务
     */
    private void startForegroundService() {
        Notification notification = notificationManager.createForegroundNotification("推送服务正在启动...");
        startForeground(notificationManager.getForegroundNotificationId(), notification);
    }

    /**
     * 初始化MQTT连接
     */
    private void initMqttConnection() {
        if (mqttConnectionManager != null) {
            // 使用电源优化配置的延迟时间
            long delayMs = powerConfig.getServiceConfig().handlerDelayMs;
            LogUtils.dTag(TAG, "延迟 " + delayMs + "ms 启动MQTT连接");

            mainHandler.postDelayed(() ->
                    mqttConnectionManager.initialize(), delayMs
            );
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        LogUtils.dTag(TAG, "MQTT推送服务销毁");

        // 停止电源状态监控
        if (powerConfig != null) {
            powerConfig.stopMonitoring();
            LogUtils.dTag(TAG, "电源状态监控已停止");
        }

        // 停止网络监听
        if (networkStateMonitor != null) {
            networkStateMonitor.release();
        }

        // 断开MQTT连接
        if (mqttConnectionManager != null) {
            mqttConnectionManager.release();
        }

        // 释放通知管理器
        if (notificationManager != null) {
            notificationManager.release();
        }

        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
        }

        super.onDestroy();
    }

    /**
     * MQTT连接状态回调
     */
    private class MqttConnectionCallback implements MqttConnectionManager.ConnectionCallback {

        @Override
        public void onConnected() {
            LogUtils.dTag(TAG, "MQTT连接成功");
            notificationManager.updateForegroundNotification("推送服务运行中，连接正常");
        }

        @Override
        public void onDisconnected() {
            LogUtils.dTag(TAG, "MQTT连接断开");
            notificationManager.updateForegroundNotification("推送服务已断开，等待重连...");
        }

        @Override
        public void onConnectionFailed(Throwable throwable) {
            LogUtils.eTag(TAG, "MQTT连接失败", throwable);
            notificationManager.updateForegroundNotification("推送服务连接失败，正在重试...");
        }

        @Override
        public void onMessageReceived(String topic, String message) {
           // LogUtils.dTag(TAG, "收到推送消息: " + topic + " -> " + message);

            // 在后台线程处理消息
            executorService.execute(() -> {
                try {
                    // 显示推送通知
                    notificationManager.showPushNotification(topic, message);
                } catch (Exception e) {
                    LogUtils.eTag(TAG, "处理推送消息失败", e);
                }
            });
        }
    }

    /**
     * 网络状态变化回调
     */
    private class NetworkStateCallback implements NetworkStateMonitor.NetworkStateCallback {

        @Override
        public void onNetworkAvailable() {
            LogUtils.dTag(TAG, "网络恢复，检查MQTT连接状态");

            // 更新通知状态：网络已恢复（延迟更新，避免与MQTT连接状态冲突）
            mainHandler.postDelayed(() -> {
                if (mqttConnectionManager != null && mqttConnectionManager.isConnected()) {
                    notificationManager.updateForegroundNotification("推送服务运行中，连接正常");
                } else {
                    notificationManager.updateForegroundNotification("网络已恢复，正在重连...");
                }
            }, 2000); // 延迟2秒，让MQTT连接状态先更新

            // 网络恢复时，如果MQTT未连接，尝试重连
            if (mqttConnectionManager != null && !mqttConnectionManager.isConnected()) {
                // 使用电源优化配置的网络重试延迟
                long retryDelayMs = powerConfig.getServiceConfig().networkRetryDelayMs;
                LogUtils.dTag(TAG, "延迟 " + retryDelayMs + "ms 后尝试重连");

                mainHandler.postDelayed(() -> {
                    if (!mqttConnectionManager.isConnected()) {
                        mqttConnectionManager.reconnect();
                    }
                }, retryDelayMs);
            }
        }

        @Override
        public void onNetworkLost() {
            LogUtils.dTag(TAG, "网络断开，MQTT连接可能受影响");
            // 网络断开时，HiveMQ客户端会自动处理重连
            // 这里只需要更新通知状态
            notificationManager.updateForegroundNotification("网络断开，等待网络恢复...");
        }

        @Override
        public void onNetworkCapabilitiesChanged(boolean hasInternet) {
            LogUtils.dTag(TAG, "网络能力变化: " + (hasInternet ? "有互联网连接" : "无互联网连接"));

            // 根据网络能力更新通知状态
            if (hasInternet) {
                if (mqttConnectionManager != null && mqttConnectionManager.isConnected()) {
                    notificationManager.updateForegroundNotification("推送服务运行中，网络正常");
                } else {
                    notificationManager.updateForegroundNotification("网络正常，正在连接推送服务...");
                }
            } else {
                notificationManager.updateForegroundNotification("网络连接异常，推送服务受影响");
            }
        }
    }

}
