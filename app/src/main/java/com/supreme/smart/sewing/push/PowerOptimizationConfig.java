package com.supreme.smart.sewing.push;

import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PHONE_KEEP_ALIVE_INTERVAL;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PHONE_MQTT_CONNECT_TIMEOUT;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PHONE_RECONNECT_INITIAL_DELAY;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PHONE_RECONNECT_MAX_DELAY;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_PHONE_SOCKET_CONNECT_TIMEOUT;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TABLET_KEEP_ALIVE_INTERVAL;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TABLET_MQTT_CONNECT_TIMEOUT;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TABLET_RECONNECT_INITIAL_DELAY;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TABLET_RECONNECT_MAX_DELAY;
import static com.supreme.smart.sewing.constants.CommonConstant.MQTT_TABLET_SOCKET_CONNECT_TIMEOUT;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_PHONE_ENABLE_SMART_RETRY;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_PHONE_MAX_RETRY_ATTEMPTS;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_PHONE_STATE_DEBOUNCE_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_TABLET_ENABLE_SMART_RETRY;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_TABLET_MAX_RETRY_ATTEMPTS;
import static com.supreme.smart.sewing.constants.CommonConstant.NETWORK_TABLET_STATE_DEBOUNCE_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_PHONE_HANDLER_DELAY_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_PHONE_NETWORK_RETRY_DELAY_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_PHONE_NOTIFICATION_UPDATE_INTERVAL_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_PHONE_THREAD_POOL_SIZE;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_PHONE_USE_FIXED_THREAD_POOL;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_TABLET_HANDLER_DELAY_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_TABLET_NETWORK_RETRY_DELAY_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_TABLET_NOTIFICATION_UPDATE_INTERVAL_MS;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_TABLET_THREAD_POOL_SIZE;
import static com.supreme.smart.sewing.constants.CommonConstant.SERVICE_TABLET_USE_FIXED_THREAD_POOL;

import android.annotation.SuppressLint;
import android.app.KeyguardManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;

import com.blankj.utilcode.util.LogUtils;
import com.supreme.smart.sewing.push.DeviceTypeDetector.DeviceType;

import java.util.Calendar;

/**
 * 电源优化配置管理器
 * 根据设备类型提供不同的电源优化策略
 */
public class PowerOptimizationConfig {

    private static final String TAG = "PowerOptimizationConfig";

    // 配置实例
    private static volatile PowerOptimizationConfig instance;
    private final Context context;
    private final DeviceType deviceType;

    // 配置参数
    private final MqttConfig mqttConfig;
    private final ServiceConfig serviceConfig;
    private final NetworkConfig networkConfig;

    // 状态监控相关
    private final Handler mainHandler;
    private final Runnable timeCheckRunnable;
    private final BroadcastReceiver screenStateReceiver;

    // 当前状态缓存
    private volatile boolean currentNightPowerSavingState = false;
    private volatile boolean isMonitoringStarted = false;

    // 防重复检查
    private volatile long lastCheckTime = 0;
    private static final long MIN_CHECK_INTERVAL = 2000; // 最小检查间隔2秒

    private PowerOptimizationConfig(Context context) {
        this.context = context.getApplicationContext();
        this.deviceType = DeviceTypeDetector.detectDeviceType(context);
        this.mainHandler = new Handler(Looper.getMainLooper());

        // 根据设备类型初始化配置
        this.mqttConfig = createMqttConfig();
        this.serviceConfig = createServiceConfig();
        this.networkConfig = createNetworkConfig();

        // 初始化监控机制
        this.timeCheckRunnable = this::checkAndUpdatePowerState;
        this.screenStateReceiver = createScreenStateReceiver();

        // 初始化当前状态
        this.currentNightPowerSavingState = shouldEnableNightPowerSaving();

        LogUtils.iTag(TAG, "电源优化配置已初始化，设备类型: " + deviceType);
        logCurrentConfig();
    }

    public static PowerOptimizationConfig getInstance(Context context) {
        if (instance == null) {
            synchronized (PowerOptimizationConfig.class) {
                if (instance == null) {
                    instance = new PowerOptimizationConfig(context);
                }
            }
        }
        return instance;
    }

    /**
     * 开始监控电源状态
     */
    public void startMonitoring() {
        if (isMonitoringStarted || deviceType == DeviceType.TABLET) {
            return;
        }

        try {
            // 注册屏幕状态广播接收器
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            filter.addAction(Intent.ACTION_USER_PRESENT);
            context.registerReceiver(screenStateReceiver, filter);

            // 开始定时检查（每分钟检查一次时间变化）
            startTimeCheck();

            isMonitoringStarted = true;
            LogUtils.iTag(TAG, "电源状态监控已启动");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "启动电源状态监控失败", e);
        }
    }

    /**
     * 停止监控电源状态
     */
    public void stopMonitoring() {
        if (!isMonitoringStarted) {
            return;
        }

        try {
            // 注销广播接收器
            context.unregisterReceiver(screenStateReceiver);

            // 停止定时检查
            stopTimeCheck();

            isMonitoringStarted = false;
            LogUtils.iTag(TAG, "电源状态监控已停止");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "停止电源状态监控失败", e);
        }
    }

    /**
     * 释放所有资源（应用退出时调用）
     */
    public void release() {
        LogUtils.iTag(TAG, "开始释放电源优化配置资源");

        try {
            // 停止监控
            stopMonitoring();

            // 清理Handler回调
            if (mainHandler != null) {
                mainHandler.removeCallbacksAndMessages(null);
                LogUtils.dTag(TAG, "已清理所有Handler回调");
            }

            // 重置状态
            currentNightPowerSavingState = false;
            isMonitoringStarted = false;
            lastCheckTime = 0;

            LogUtils.iTag(TAG, "电源优化配置资源释放完成");

        } catch (Exception e) {
            LogUtils.eTag(TAG, "释放电源优化配置资源失败", e);
        }
    }

    /**
     * 创建屏幕状态广播接收器
     */
    private BroadcastReceiver createScreenStateReceiver() {
        return new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent == null || intent.getAction() == null) {
                    LogUtils.wTag(TAG, "收到空的广播Intent");
                    return;
                }

                String action = intent.getAction();
                LogUtils.iTag(TAG, "收到屏幕状态广播: " + action);

                // 根据不同的广播类型设置不同的延迟
                long delay = getDelayForAction(action);
                LogUtils.dTag(TAG, "广播 " + action + " 设置延迟: " + delay + "ms");

                if (delay > 0) {
                    // 屏幕状态变化时检查电源状态
                    LogUtils.dTag(TAG, "调度电源状态检查，延迟: " + delay + "ms");
                    mainHandler.postDelayed(() -> {
                        LogUtils.dTag(TAG, "执行延迟后的电源状态检查 (触发广播: " + action + ")");
                        checkAndUpdatePowerStateWithThrottle();
                    }, delay);
                } else {
                    LogUtils.wTag(TAG, "未知广播类型，跳过处理: " + action);
                }
            }

            private long getDelayForAction(String action) {
                switch (action) {
                    case Intent.ACTION_SCREEN_OFF:
                        // 屏幕关闭立即检查（可能进入省电模式）
                        return 500;
                    case Intent.ACTION_SCREEN_ON:
                        // 屏幕开启稍微延迟，避免与USER_PRESENT重复
                        return 1500;
                    case Intent.ACTION_USER_PRESENT:
                        // 用户解锁立即检查（退出省电模式）
                        return 200;
                    default:
                        return 0;
                }
            }
        };
    }

    /**
     * 开始定时检查时间变化
     */
    private void startTimeCheck() {
        stopTimeCheck(); // 先停止之前的检查
        // 每分钟检查一次时间变化
        mainHandler.postDelayed(timeCheckRunnable, 60 * 1000);
    }

    /**
     * 停止定时检查
     */
    private void stopTimeCheck() {
        mainHandler.removeCallbacks(timeCheckRunnable);
    }

    /**
     * 检查并更新电源状态（带节流控制）
     */
    private void checkAndUpdatePowerStateWithThrottle() {
        long currentTime = System.currentTimeMillis();
        LogUtils.dTag(TAG, "开始电源状态检查（带节流控制）");

        // 节流控制：避免短时间内重复检查
        if (currentTime - lastCheckTime < MIN_CHECK_INTERVAL) {
            LogUtils.dTag(TAG, "跳过重复检查，距离上次检查仅 " + (currentTime - lastCheckTime) + "ms");
            return;
        }

        lastCheckTime = currentTime;
        LogUtils.dTag(TAG, "通过节流检查，执行电源状态检查");
        checkAndUpdatePowerState();
    }

    /**
     * 检查并更新电源状态
     */
    private void checkAndUpdatePowerState() {
        try {
            // 详细记录当前状态
            boolean isNight = isNightTime();
            boolean isLocked = isScreenLocked();
            boolean newState = shouldEnableNightPowerSaving();
            if (newState != currentNightPowerSavingState) {
                LogUtils.iTag(TAG, "🔋 夜间省电模式状态变化: " + currentNightPowerSavingState + " -> " + newState);
                LogUtils.iTag(TAG, "触发原因 - 夜间时段: " + isNight + ", 锁屏状态: " + isLocked);
                currentNightPowerSavingState = newState;

                // 记录当前动态配置
                logCurrentDynamicConfig();
            }

            // 继续下一次检查（仅定时器调用时）
            if (isMonitoringStarted && Thread.currentThread().equals(Looper.getMainLooper().getThread())) {
                mainHandler.postDelayed(timeCheckRunnable, 60 * 1000);
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查电源状态失败", e);
        }
    }

    /**
     * MQTT相关配置
     */
    public static class MqttConfig {
        /**
         *
         */
        public final int keepAliveInterval;
        /**
         * 重连初始延迟（毫秒）
         */
        public final int reconnectInitialDelay;
        /**
         * 重连最大延迟（毫秒）
         */
        public final int reconnectMaxDelay;
        /**
         * Socket连接超时（毫秒）
         */
        public final int socketConnectTimeout;
        /**
         * MQTT连接超时（毫秒）
         */
        public final int mqttConnectTimeout;

        public MqttConfig(int keepAliveInterval,
                          int reconnectInitialDelay,
                          int reconnectMaxDelay,
                          int socketConnectTimeout,
                          int mqttConnectTimeout) {
            this.keepAliveInterval = keepAliveInterval;
            this.reconnectInitialDelay = reconnectInitialDelay;
            this.reconnectMaxDelay = reconnectMaxDelay;
            this.socketConnectTimeout = socketConnectTimeout;
            this.mqttConnectTimeout = mqttConnectTimeout;
        }
    }

    /**
     * 服务相关配置
     */
    public static class ServiceConfig {
        /**
         * 线程池大小
         */
        public final int threadPoolSize;
        /**
         * Handler延迟时间（毫秒）
         */
        public final long handlerDelayMs;
        /**
         * 网络重试延迟（毫秒）
         */
        public final long networkRetryDelayMs;
        /**
         * 通知更新间隔（毫秒）
         */
        public final long notificationUpdateIntervalMs;
        /**
         * 是否使用固定大小线程池
         */
        public final boolean useFixedThreadPool;

        public ServiceConfig(int threadPoolSize,
                             long handlerDelayMs,
                             long networkRetryDelayMs,
                             long notificationUpdateIntervalMs,
                             boolean useFixedThreadPool) {
            this.threadPoolSize = threadPoolSize;
            this.handlerDelayMs = handlerDelayMs;
            this.networkRetryDelayMs = networkRetryDelayMs;
            this.notificationUpdateIntervalMs = notificationUpdateIntervalMs;
            this.useFixedThreadPool = useFixedThreadPool;
        }
    }

    /**
     * 网络相关配置
     */
    public static class NetworkConfig {
        /**
         * 网络状态防抖时间（毫秒）
         */
        public final long networkStateDebounceMs;
        /**
         * 最大重试次数
         */
        public final int maxRetryAttempts;
        /**
         * 是否启用智能重试
         */
        public final boolean enableSmartRetry;

        public NetworkConfig(long networkStateDebounceMs,
                             int maxRetryAttempts,
                             boolean enableSmartRetry) {
            this.networkStateDebounceMs = networkStateDebounceMs;
            this.maxRetryAttempts = maxRetryAttempts;
            this.enableSmartRetry = enableSmartRetry;
        }
    }

    /**
     * 创建MQTT配置
     */
    private MqttConfig createMqttConfig() {
        if (deviceType == DeviceType.TABLET) {
            // 工业平板：不考虑电源消耗，优先保证连接稳定性
            return new MqttConfig(
                    MQTT_TABLET_KEEP_ALIVE_INTERVAL / 1000,   // 心跳
                    MQTT_TABLET_RECONNECT_INITIAL_DELAY,      // 重连延迟
                    MQTT_TABLET_RECONNECT_MAX_DELAY,          // 最大延迟
                    MQTT_TABLET_SOCKET_CONNECT_TIMEOUT,       // Socket超时
                    MQTT_TABLET_MQTT_CONNECT_TIMEOUT          // MQTT超时
            );
        } else {
            // 手机：优化电源消耗
            return new MqttConfig(
                    MQTT_PHONE_KEEP_ALIVE_INTERVAL / 1000,   // 心跳
                    MQTT_PHONE_RECONNECT_INITIAL_DELAY,      // 重连延迟
                    MQTT_PHONE_RECONNECT_MAX_DELAY,          // 最大延迟
                    MQTT_PHONE_SOCKET_CONNECT_TIMEOUT,       // Socket超时
                    MQTT_PHONE_MQTT_CONNECT_TIMEOUT          // MQTT超时
            );
        }
    }

    /**
     * 创建服务配置
     */
    private ServiceConfig createServiceConfig() {
        if (deviceType == DeviceType.TABLET) {
            // 平板设备：性能优先
            return new ServiceConfig(
                    SERVICE_TABLET_THREAD_POOL_SIZE,                    // 线程池大小
                    SERVICE_TABLET_HANDLER_DELAY_MS,                    // Handler延迟
                    SERVICE_TABLET_NETWORK_RETRY_DELAY_MS,              // 网络重试延迟
                    SERVICE_TABLET_NOTIFICATION_UPDATE_INTERVAL_MS,     // 通知更新间隔
                    SERVICE_TABLET_USE_FIXED_THREAD_POOL                // 使用固定线程池
            );
        } else {
            // 手机：电源优化
            return new ServiceConfig(
                    SERVICE_PHONE_THREAD_POOL_SIZE,                     // 线程池大小
                    SERVICE_PHONE_HANDLER_DELAY_MS,                     // Handler延迟
                    SERVICE_PHONE_NETWORK_RETRY_DELAY_MS,               // 网络重试延迟
                    SERVICE_PHONE_NOTIFICATION_UPDATE_INTERVAL_MS,      // 通知更新间隔
                    SERVICE_PHONE_USE_FIXED_THREAD_POOL                 // 使用固定线程池
            );
        }
    }

    /**
     * 创建网络配置
     */
    private NetworkConfig createNetworkConfig() {
        if (deviceType == DeviceType.TABLET) {
            // 平板设备：快速响应
            return new NetworkConfig(
                    NETWORK_TABLET_STATE_DEBOUNCE_MS,       // 网络状态防抖时间
                    NETWORK_TABLET_MAX_RETRY_ATTEMPTS,      // 最大重试次数
                    NETWORK_TABLET_ENABLE_SMART_RETRY       // 启用智能重试
            );
        } else {
            // 手机：减少网络活动
            return new NetworkConfig(
                    NETWORK_PHONE_STATE_DEBOUNCE_MS,        // 网络状态防抖时间
                    NETWORK_PHONE_MAX_RETRY_ATTEMPTS,       // 最大重试次数
                    NETWORK_PHONE_ENABLE_SMART_RETRY        // 启用智能重试
            );
        }
    }

    /**
     * 检查是否处于省电模式
     */
    @SuppressLint("ObsoleteSdkInt")
    public boolean isPowerSaveMode() {

        try {
            if (deviceType == DeviceType.TABLET) {
                // 工业平板通常不需要考虑省电模式
                return false;
            }

            PowerManager powerManager = (PowerManager) context.
                    getSystemService(Context.POWER_SERVICE);
            if (powerManager != null && android.os.Build.VERSION.SDK_INT
                    >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                return powerManager.isPowerSaveMode();
            }
            return false;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查省电模式失败", e);
            return false;
        }
    }

    /**
     * 检查是否处于Doze模式
     */
    @SuppressLint("ObsoleteSdkInt")
    public boolean isDeviceIdleMode() {
        try {
            if (deviceType == DeviceType.TABLET) {
                // 工业平板通常不进入Doze模式
                return false;
            }

            PowerManager powerManager = (PowerManager) context.
                    getSystemService(Context.POWER_SERVICE);
            if (powerManager != null && android.os.Build.VERSION.SDK_INT
                    >= android.os.Build.VERSION_CODES.M) {
                return powerManager.isDeviceIdleMode();
            }
            return false;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查Doze模式失败", e);
            return false;
        }
    }

    /**
     * 检查是否处于夜间时段（20:00~8:00）
     */
    public boolean isNightTime() {
        try {
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            // 夜间时段：20:00~23:59 或 0:00~7:59
            return hour >= 20 || hour < 8;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查夜间时段失败", e);
            return false;
        }
    }

    /**
     * 检查设备是否处于锁屏状态
     */
    public boolean isScreenLocked() {
        try {
            KeyguardManager keyguardManager = (KeyguardManager) context.
                    getSystemService(Context.KEYGUARD_SERVICE);
            if (keyguardManager != null) {
                return keyguardManager.isKeyguardLocked();
            }
            return false;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查锁屏状态失败", e);
            return false;
        }
    }

    /**
     * 检查是否应该启用夜间省电模式
     * 条件：夜间时段(20:00~8:00) + 锁屏状态
     */
    public boolean shouldEnableNightPowerSaving() {
        if (deviceType == DeviceType.TABLET) {
            // 工业平板通常不需要夜间省电
            return false;
        }
        return isNightTime() && isScreenLocked();
    }

    /**
     * 获取当前缓存的夜间省电状态
     * 推荐使用此方法，避免频繁的系统调用
     */
    public boolean isNightPowerSavingEnabled() {
        return currentNightPowerSavingState;
    }

    /**
     * 获取当前应该使用的MQTT心跳间隔
     * 根据电源状态动态调整
     */
    public int getCurrentKeepAliveInterval() {
        int baseInterval = mqttConfig.keepAliveInterval;

        if (deviceType == DeviceType.PHONE) {
            // 手机需要根据电源状态调整
            if (isNightPowerSavingEnabled()) {
                // 夜间锁屏状态下，延长3倍时间
                LogUtils.dTag(TAG, "夜间省电模式：延长心跳间隔3倍");
                return Math.min(baseInterval * 3, 900); // 最长15分钟
            } else if (isPowerSaveMode()) {
                // 省电模式下，延长心跳间隔
                return Math.min(baseInterval * 2, 600); // 最长10分钟
            } else if (isDeviceIdleMode()) {
                // Doze模式下，进一步延长
                return Math.min(baseInterval * 3, 900); // 最长15分钟
            }
        }

        return baseInterval;
    }

    /**
     * 获取当前应该使用的通知更新间隔
     * 根据电源状态和夜间模式动态调整
     */
    public long getCurrentNotificationUpdateInterval() {
        long baseInterval = serviceConfig.notificationUpdateIntervalMs;

        if (deviceType == DeviceType.PHONE) {
            if (isNightPowerSavingEnabled()) {
                // 夜间锁屏状态下，延长3倍时间
                LogUtils.dTag(TAG, "夜间省电模式：延长通知更新间隔3倍");
                return Math.min(baseInterval * 3, 5 * 60 * 1000); // 最长5分钟
            } else if (isPowerSaveMode()) {
                // 省电模式下，延长间隔
                return Math.min(baseInterval * 2, 2 * 60 * 1000); // 最长2分钟
            }
        }

        return baseInterval;
    }

    /**
     * 获取当前应该使用的网络重试延迟
     * 根据电源状态和夜间模式动态调整
     */
    public long getCurrentNetworkRetryDelay() {
        long baseDelay = serviceConfig.networkRetryDelayMs;

        if (deviceType == DeviceType.PHONE) {
            if (isNightPowerSavingEnabled()) {
                // 夜间锁屏状态下，延长3倍时间
                LogUtils.dTag(TAG, "夜间省电模式：延长网络重试延迟3倍");
                return Math.min(baseDelay * 3, 30 * 1000); // 最长30秒
            } else if (isPowerSaveMode()) {
                // 省电模式下，延长延迟
                return Math.min(baseDelay * 2, 20 * 1000); // 最长20秒
            }
        }

        return baseDelay;
    }

    /**
     * 获取当前应该使用的Handler延迟
     * 根据电源状态和夜间模式动态调整
     */
    public long getCurrentHandlerDelay() {
        long baseDelay = serviceConfig.handlerDelayMs;

        if (deviceType == DeviceType.PHONE) {
            if (isNightPowerSavingEnabled()) {
                // 夜间锁屏状态下，延长3倍时间
                LogUtils.dTag(TAG, "夜间省电模式：延长Handler延迟3倍");
                return Math.min(baseDelay * 3, 10 * 1000); // 最长10秒
            } else if (isPowerSaveMode()) {
                // 省电模式下，延长延迟
                return Math.min(baseDelay * 2, 5 * 1000); // 最长5秒
            }
        }

        return baseDelay;
    }

    /**
     * 记录当前配置
     */
    private void logCurrentConfig() {
        LogUtils.iTag(TAG, "=== 电源优化配置 ===");
        LogUtils.iTag(TAG, "设备类型: " + deviceType);
        LogUtils.iTag(TAG, "MQTT心跳间隔: " + mqttConfig.keepAliveInterval + "秒");
        LogUtils.iTag(TAG, "线程池大小: " + serviceConfig.threadPoolSize);
        LogUtils.iTag(TAG, "网络防抖时间: " + networkConfig.networkStateDebounceMs + "毫秒");
        LogUtils.iTag(TAG, "通知更新间隔: " + serviceConfig.notificationUpdateIntervalMs + "毫秒");
        LogUtils.iTag(TAG, "夜间时段: " + isNightTime());
        LogUtils.iTag(TAG, "锁屏状态: " + isScreenLocked());
        LogUtils.iTag(TAG, "夜间省电模式: " + shouldEnableNightPowerSaving());
        LogUtils.iTag(TAG, "==================");
    }

    /**
     * 记录当前动态配置状态
     */
    public void logCurrentDynamicConfig() {
        LogUtils.iTag(TAG, "=== 当前动态配置 ===");
        LogUtils.iTag(TAG, "当前MQTT心跳间隔: " + getCurrentKeepAliveInterval() + "秒");
        LogUtils.iTag(TAG, "当前通知更新间隔: " + getCurrentNotificationUpdateInterval() + "毫秒");
        LogUtils.iTag(TAG, "当前网络重试延迟: " + getCurrentNetworkRetryDelay() + "毫秒");
        LogUtils.iTag(TAG, "当前Handler延迟: " + getCurrentHandlerDelay() + "毫秒");
        LogUtils.iTag(TAG, "省电模式: " + isPowerSaveMode());
        LogUtils.iTag(TAG, "Doze模式: " + isDeviceIdleMode());
        LogUtils.iTag(TAG, "夜间省电模式: " + shouldEnableNightPowerSaving());
        LogUtils.iTag(TAG, "==================");
    }

    // Getter方法
    public MqttConfig getMqttConfig() {
        return mqttConfig;
    }

    public ServiceConfig getServiceConfig() {
        return serviceConfig;
    }

    public NetworkConfig getNetworkConfig() {
        return networkConfig;
    }

    public DeviceType getDeviceType() {
        return deviceType;
    }
}
